/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */

const proxyTarget = 'https://dev.evshine.net'; // 开发环境
// const proxyTarget = 'https://test.evshine.net'; // 测试A
// const proxyTarget = 'https://test.xdtev.cn'; // 测试B
// const proxyTarget = 'https://release.evshine.net'; // 预发布
const proxyCookie = `EUNOMIASESSIONID=783e90be-8dc5-4a72-897e-974f2012b602; portal_idaas_sid="U1lTQURNSU4="; XDT-DEVA-JSession=YjJhMDg4M2YtNDE0NS00YTgzLWIzNDktNWZiOWEyZGVlYjk4`;

module.exports = {
    dev: {
        '/idaasApi': {
            target: proxyTarget,
            changeOrigin: true,
        },
        '/mng-': {
            target: proxyTarget,
            changeOrigin: true,
            onProxyReq(proxyReq, req, res) {
                proxyReq.setHeader('cookie', proxyCookie);
            },
            onProxyRes(proxyRes, req, res) {},
        },
        '/everest/webapi': {
            target: proxyTarget,
            changeOrigin: true,
            // pathRewrite: { '^/webapi': '' },
            onProxyReq(proxyReq, req, res) {
                proxyReq.setHeader('cookie', proxyCookie);
            },
            onProxyRes(proxyRes, req, res) {},
        },
        '/api/': {
            target: 'https://preview.pro.ant.design',
            changeOrigin: true,
            pathRewrite: {
                '^': '',
            },
            onProxyReq(proxyReq, req, res) {
                proxyReq.setHeader(
                    'cookie',
                    'EUNOMIASESSIONID=cce582c2-7bce-417f-b388-919b5c4781bf',
                );
            },
            onProxyRes(proxyRes, req, res) {},
        },
    },
    test: {
        '/api/': {
            target: 'https://preview.pro.ant.design',
            changeOrigin: true,
            pathRewrite: {
                '^': '',
            },
        },
    },
    pre: {
        '/api/': {
            target: 'your pre url',
            changeOrigin: true,
            pathRewrite: {
                '^': '',
            },
        },
    },
};
