import { ButtonProps, PopoverProps, RowProps, ColProps, SpaceProps } from 'antd';
import React from 'react';

export interface BaseButtonConfig {
    props?: ButtonProps;
    style?: React.CSSProperties;
    children?: BaseButtonConfig[];
    onClick?: (row: any, e?: React.MouseEvent) => Promise<void> | void;
    show?: boolean | ((row: any) => boolean);
    label?: string;
}

export interface BaseButtonsProps {
    btns: BaseButtonConfig[] | ((row: any) => BaseButtonConfig[]);
    row?: any;
    rowProps?: RowProps;
    colProps?: ColProps;
    maxCount?: number;
    loading?: boolean;
    popoverProps?: PopoverProps;
    ellipsisSpaceProps?: SpaceProps;
    [key: string]: any;
}

export interface PopoverButtonProps {
    btn: BaseButtonConfig;
    row?: any;
    loading?: boolean;
    children?: React.ReactElement | React.ReactElement[];
    popoverProps?: PopoverProps;
    spaceProps?: SpaceProps;
    className?: string;
    [key: string]: any;
}

export interface CustomButtonProps {
    btn: BaseButtonConfig;
    children?: React.ReactElement | React.ReactElement[];
    row?: any;
    pLoading?: boolean;
    className?: string;
    [key: string]: any;
}
