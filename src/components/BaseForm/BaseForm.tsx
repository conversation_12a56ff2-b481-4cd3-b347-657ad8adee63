import React, {
    useState,
    useImperativeHandle,
    forwardRef,
    useMemo,
    useEffect,
    useCallback,
    useRef,
} from 'react';
import { Form, Row, Col, processFormConfig, createPureFormElement } from './deps';
import { BaseButtons, BaseButtonConfig } from '@/components/BaseButtons';
import { initParams } from './utils';
import { BaseFormProps, BaseFormRef, FormConfig, InferModelValueFromConfig } from './types';
import './index.less';

// 常量定义
const DEFAULT_ITEM_SPAN = 12;
const DEFAULT_FILTER_MAX_COUNT = 7;
const GRID_TOTAL_COLUMNS = 24;
const FORM_RESET_DELAY = 0;

/**
 * @name BaseForm
 * @description 基于umi3.5+antd的增强表单组件，主要功能：
 * 1. 通过配置项生成表单项，支持自定义渲染
 * 2. 支持筛选器模式
 * 3. 组件会自动初始化表单数据对象
 * 4. 支持使用受控模式进行双向绑定
 * 5. 配置项 required 设置为 'check'，即可快捷设置必填校验
 * 6. 支持多级field设置
 * 7. 优化了性能，使用了React.memo和useCallback
 * 8. 改进了TypeScript类型安全性
 * 9. 支持根据config自动推导modelValue类型
 */
function BaseFormComponent<TConfig extends readonly FormConfig[] = FormConfig[], TModelValue = any>(
    props: BaseFormProps<TConfig, TModelValue>,
    ref: React.Ref<BaseFormRef>,
) {
    const {
        config = [],
        modelValue = {} as TModelValue,
        itemLayout = { span: DEFAULT_ITEM_SPAN },
        preview = false,
        disabled = false,
        isFilter = false,
        filterLoading = false,
        filterMaxCount = DEFAULT_FILTER_MAX_COUNT,
        filterButtons = ['expand', 'reset', 'submit'],
        btnsLayout = {
            type: 'flex' as const,
            align: 'bottom' as const,
            justify: 'end' as const,
        },
        flexLayoutMode = {
            labelSpan: 8,
            contentSpan: 16,
            gutter: 8,
        },
        onUpdateModelValue,
        onItemChange,
        onReset,
        onSubmit,
        onLoadData,
        ...formProps
    } = props;
    const [form] = Form.useForm();
    const [isFilterExpand, setIsFilterExpand] = useState(false);

    // 使用ref来存储最新的值，避免依赖循环
    const modelValueRef = useRef(modelValue);
    const onUpdateModelValueRef = useRef(onUpdateModelValue);
    const onItemChangeRef = useRef(onItemChange);
    const onLoadDataRef = useRef(onLoadData);
    const onSubmitRef = useRef(onSubmit);
    const onResetRef = useRef(onReset);

    // 更新ref的值
    modelValueRef.current = modelValue;
    onUpdateModelValueRef.current = onUpdateModelValue;
    onItemChangeRef.current = onItemChange;
    onLoadDataRef.current = onLoadData;
    onSubmitRef.current = onSubmit;
    onResetRef.current = onReset;

    // 显示的配置项
    const showConfig = useMemo(() => {
        return (
            config?.filter((item) => {
                return typeof item?.show === 'function'
                    ? item.show(modelValue)
                    : item.show !== false;
            }) || []
        );
    }, [config, modelValue]);

    // 处理后的配置项
    const processedConfig = useMemo(() => {
        let cpConfig = showConfig.map((item) =>
            processFormConfig({
                item,
                params: modelValue,
                formProps: { preview, disabled, ...formProps },
            }),
        );

        if (isFilter && !isFilterExpand) {
            cpConfig = cpConfig.slice(0, filterMaxCount);
        }

        return cpConfig;
    }, [
        showConfig,
        modelValue,
        preview,
        disabled,
        formProps,
        isFilter,
        isFilterExpand,
        filterMaxCount,
    ]);

    // 监听配置变化，初始化表单数据
    useEffect(() => {
        if (!Array.isArray(config) || config.length === 0) return;

        const currentModelValue = modelValueRef.current;
        const params = initParams(config, currentModelValue);

        // 只有当确实有新的必需参数时才更新
        const hasNewRequiredParams = Object.keys(params).some((key) => {
            const configItem = (config as FormConfig[])?.find((item) => item.field === key);
            return (
                params[key] !== undefined &&
                (currentModelValue as any)[key] === undefined &&
                configItem?.defaultValue !== undefined
            );
        });

        if (hasNewRequiredParams && onUpdateModelValueRef.current) {
            onUpdateModelValueRef.current({ ...currentModelValue, ...params });
        }

        // 延迟重置表单字段，避免与状态更新冲突
        const timer = setTimeout(() => {
            form.resetFields();
        }, FORM_RESET_DELAY);

        return () => clearTimeout(timer);
    }, [config, form]);

    // 只在初始化时同步外部数据到表单
    useEffect(() => {
        form.setFieldsValue(modelValue);
    }, [form]); // 只依赖form，不依赖modelValue

    // 提交表单
    const submit = useCallback(
        async (params = {}) => {
            try {
                await form.validateFields();
            } catch (e) {
                return;
            }

            if (onLoadDataRef.current) {
                onLoadDataRef.current({ requestFrom: 'filterSearch', ...params });
            }

            if (onSubmitRef.current) {
                onSubmitRef.current(modelValueRef.current);
            }
        },
        [form], // 只依赖form
    );

    // 重置表单
    const reset = useCallback(() => {
        const resetParams = initParams(config as FormConfig[]);
        if (onUpdateModelValueRef.current) {
            onUpdateModelValueRef.current(resetParams);
        }

        setTimeout(() => {
            form.resetFields();
        }, FORM_RESET_DELAY);

        if (onResetRef.current) {
            onResetRef.current();
        }

        submit();
    }, [config, form, submit]); // 保持必要的依赖

    // 筛选按钮配置
    const finalFilterButtons = useMemo(() => {
        const btnsMap: Record<string, BaseButtonConfig> = {
            expand: {
                label: isFilterExpand ? '收起' : '展开',
                onClick: () => setIsFilterExpand(!isFilterExpand),
                props: {
                    size: 'small',
                    type: 'link',
                },
                show: flexLayoutMode && showConfig.length > filterMaxCount,
            },
            reset: {
                label: '重置',
                onClick: reset,
                props: {
                    type: 'default',
                },
            },
            submit: {
                label: '查询',
                onClick: submit,
                props: {
                    type: 'primary',
                },
            },
        };

        const btns: BaseButtonConfig[] = [];
        filterButtons.forEach((x) => {
            if (typeof x === 'string' && btnsMap[x]) {
                btns.push(btnsMap[x]);
            } else if (typeof x === 'object' && x !== null) {
                btns.push(x);
            } else {
                console.error('无效的按钮配置', x);
            }
        });

        return btns;
    }, [
        isFilterExpand,
        flexLayoutMode,
        showConfig.length,
        filterMaxCount,
        filterButtons,
        reset,
        submit,
    ]);

    // 暴露方法
    useImperativeHandle(ref, () => ({
        validate: () => form.validateFields(),
        validateFields: (fields) => form.validateFields(fields),
        resetFields: () => form.resetFields(),
        clearValidate: (fields) =>
            form.setFields(
                (fields || Object.keys(form.getFieldsValue())).map((field) => ({
                    name: field,
                    errors: [],
                })),
            ),
        reset,
        submit,
    }));

    // 处理表单值变化 - 简化版本，移除防抖
    const handleValuesChange = useCallback((changedValues: any, allValues: any) => {
        console.log('🔄 Form values changed:', changedValues, allValues);

        // 立即处理单个字段变化回调
        if (onItemChangeRef.current) {
            Object.keys(changedValues).forEach((field) => {
                onItemChangeRef.current!(field, changedValues[field]);
            });
        }

        // 立即处理外部数据更新，但由于React.memo会阻止不必要的重新渲染
        if (onUpdateModelValueRef.current) {
            onUpdateModelValueRef.current(allValues);
        }
    }, []);

    // 创建纯表单元素 - 完全依赖Form.Item的name属性控制
    const createFormContent = useCallback(
        (item: FormConfig): React.ReactNode => {
            // 处理自定义渲染函数
            if (typeof item.element === 'function') {
                const elementType = item.element as any;
                if (elementType.length === 3) {
                    // 自定义渲染函数：(createElement, item, modelValue) => ReactElement
                    // 获取当前Form的值
                    const currentValues = form.getFieldsValue();
                    return elementType(React.createElement, item, currentValues);
                }
            }

            // 创建纯组件，不传递value和onChange，完全依赖Form.Item控制
            return createPureFormElement(item);
        },
        [form],
    );

    // 计算按钮区域的偏移量
    const { buttonSpan, offset } = useMemo(() => {
        let totalCount = 0;
        processedConfig.forEach((item) => {
            const colProps =
                typeof item.colProps === 'number'
                    ? { span: item.colProps }
                    : { span: itemLayout.span || DEFAULT_ITEM_SPAN, ...item.colProps };
            totalCount += (colProps.span || 0) + (colProps.offset || 0);
        });

        const buttonSpan = itemLayout.span || DEFAULT_ITEM_SPAN;
        const remainingInLastRow = totalCount % GRID_TOTAL_COLUMNS;
        const offset =
            GRID_TOTAL_COLUMNS - remainingInLastRow - buttonSpan >= 0
                ? GRID_TOTAL_COLUMNS - remainingInLastRow - buttonSpan
                : GRID_TOTAL_COLUMNS - buttonSpan;

        return { buttonSpan, offset };
    }, [processedConfig, itemLayout.span]);

    return (
        <Form
            form={form}
            initialValues={modelValue as any}
            onValuesChange={handleValuesChange}
            {...formProps}
            className={`base-form ${isFilter ? 'base-form-filter' : ''} ${
                formProps.className || ''
            }`}
        >
            <Row gutter={itemLayout.gutter}>
                {processedConfig.map((item, index) => {
                    // 确保有有效的field或使用index作为key
                    const key = item.field || `form-item-${index}`;

                    const colProps =
                        typeof item.colProps === 'number'
                            ? { span: item.colProps }
                            : { span: itemLayout.span || DEFAULT_ITEM_SPAN, ...item.colProps };

                    const rules =
                        preview || disabled || item.disabled
                            ? undefined
                            : [
                                  ...(item.required === 'check'
                                      ? [
                                            {
                                                required: true,
                                                message:
                                                    item.props?.placeholder ||
                                                    `请输入${item.title}`,
                                            },
                                        ]
                                      : []),
                                  ...(item?.itemProps?.rules || item?.rules || []),
                              ];

                    return (
                        <Col key={key} {...colProps}>
                            <Form.Item
                                label={item.title}
                                name={item.field}
                                rules={rules}
                                style={item.itemStyle}
                                {...item.itemProps}
                            >
                                {createFormContent(item)}
                            </Form.Item>
                        </Col>
                    );
                })}

                {isFilter && (
                    <Col span={buttonSpan} offset={offset} className="form-item-row-search">
                        <Form.Item wrapperCol={{ span: 24 }}>
                            <BaseButtons
                                btns={finalFilterButtons}
                                row={modelValue}
                                rowProps={btnsLayout}
                                loading={filterLoading}
                                style={{
                                    display: 'inline-block',
                                    width: '100%',
                                    marginLeft: '5px',
                                }}
                            />
                        </Form.Item>
                    </Col>
                )}
            </Row>
        </Form>
    );
}

// 使用forwardRef包装组件以支持泛型
const BaseForm = forwardRef(BaseFormComponent) as <
    TConfig extends readonly FormConfig[] = FormConfig[],
    TModelValue = any,
>(
    props: BaseFormProps<TConfig, TModelValue> & { ref?: React.Ref<BaseFormRef> },
) => React.ReactElement;

// 暂时移除React.memo进行测试

// 手动设置displayName
(BaseForm as any).displayName = 'BaseForm';

// 创建一个工厂函数来生成带类型推导的BaseForm
export function createTypedBaseForm<TConfig extends readonly FormConfig[]>(config: TConfig) {
    // 推导出的ModelValue类型
    type InferredModelValue = InferModelValueFromConfig<TConfig>;

    // 返回类型化的组件和类型
    return {
        // 类型化的BaseForm组件
        BaseForm: BaseForm as <TModelValue = InferredModelValue>(
            props: Omit<BaseFormProps<TConfig, TModelValue>, 'config'> & {
                config?: TConfig;
                modelValue?: TModelValue;
                onUpdateModelValue?: (value: TModelValue) => void;
                ref?: React.Ref<BaseFormRef>;
            },
        ) => React.ReactElement,

        // 导出推导出的类型
        ModelValueType: {} as InferredModelValue,

        // 配置
        config,

        // 类型工具
        createInitialValue: (): InferredModelValue => {
            const result = {} as any;
            config.forEach((item) => {
                if (item.defaultValue !== undefined) {
                    result[item.field] = item.defaultValue;
                }
            });
            return result;
        },
    };
}

// 创建一个宏函数，用于在组件文件中直接定义和导出类型
export function defineTypedForm<TConfig extends readonly FormConfig[]>(
    config: TConfig,
): {
    config: TConfig;
    FormData: InferModelValueFromConfig<TConfig>;
    createInitialValue: () => InferModelValueFromConfig<TConfig>;
} {
    const createInitialValue = (): InferModelValueFromConfig<TConfig> => {
        const result = {} as any;
        config.forEach((item) => {
            if (item.defaultValue !== undefined) {
                result[item.field] = item.defaultValue;
            }
        });
        return result;
    };

    return {
        config,
        FormData: {} as InferModelValueFromConfig<TConfig>,
        createInitialValue,
    };
}

// 暂时移除React.memo，直接导出BaseForm进行测试
export default BaseForm;
