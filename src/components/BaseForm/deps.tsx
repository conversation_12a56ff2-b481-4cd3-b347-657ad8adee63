// antd (React版本) - umi3.5 + antd
import React from 'react';
export { Form, Row, Col } from 'antd';
import { Input, Select, Switch, Checkbox, Radio } from 'antd';
import type { FormConfig } from './types';
import { isReactNode, deepGet } from './utils';

const { Option } = Select;

// popover内容、按钮与其各自对应的插槽,默认为antd的prop名称
export const PopoverSlotMap = {
    content: 'content',
    button: 'default',
};

export const noBorderButtonProp = {
    type: 'link' as const,
};

/**
 * 处理表单配置项
 */
export function processFormConfig({
    item,
    params: _params,
    formProps = {},
}: {
    item: FormConfig;
    params: any;
    formProps?: any;
}): FormConfig {
    // 处理disabled状态
    const disabled = formProps.disabled || item.disabled;

    // 处理props
    const processedProps = {
        ...item.props,
        disabled,
    };

    return {
        ...item,
        props: processedProps,
    };
}

/**
 * 创建表单元素 - 优化版本，减少不必要的重新渲染
 */
export function createFormElement(
    item: FormConfig,
    params: any,
    onChange: (value: any) => void,
): React.ReactNode {
    const { element, props = {}, field } = item;
    const value = deepGet(params, field);

    const commonProps = {
        ...props,
        value,
        onChange,
    };

    // 针对特定组件的特殊处理
    switch (element) {
        case Select:
            return (
                <Select {...commonProps}>
                    {props.options?.map((option: any) => (
                        <Option key={option.value} value={option.value}>
                            {option.label}
                        </Option>
                    ))}
                </Select>
            );
        case Switch:
            return <Switch {...commonProps} checked={value} />;
        case Checkbox:
            return <Checkbox {...commonProps} checked={value} />;
        case Radio:
            return <Radio {...commonProps} checked={value} />;
        default:
            // 处理其他所有情况
            break;
    }
    let mainDom: React.ReactNode = <Input {...commonProps} />;

    // 如果element是React组件
    if (React.isValidElement(element)) {
        // 使用cloneElement而不是重新创建，保持组件引用稳定性
        mainDom = React.cloneElement(element, commonProps);
    } else if (typeof element === 'function') {
        // 检查是否是渲染函数（通过参数长度判断）
        try {
            // 如果函数接受2个或更多参数，认为是渲染函数
            const renderFn = element as (
                createElement: typeof React.createElement,
                props: any,
            ) => React.ReactNode;
            mainDom = renderFn(React.createElement, commonProps);
        } catch (error) {
            // 如果出错，回退到使用createElement
            const Component = element as React.ComponentType<any>;
            mainDom = React.createElement(Component, commonProps);
        }
    }

    if (item.brotherNodes) {
        return (
            <>
                {mainDom}
                {typeof item.brotherNodes === 'function'
                    ? item.brotherNodes(React.createElement)
                    : item.brotherNodes}
            </>
        );
    }
    return mainDom;
}

/**
 * 创建表单元素 - 不受控版本，让Antd Form完全控制
 */
export function createFormElementWithoutControl(item: FormConfig): React.ReactNode {
    const { element, props = {} } = item;

    // 只传递props，不传递value和onChange
    const elementProps = { ...props };

    // 针对特定组件的特殊处理
    switch (element) {
        case Select:
            return (
                <Select {...elementProps}>
                    {props.options?.map((option: any) => (
                        <Option key={option.value} value={option.value}>
                            {option.label}
                        </Option>
                    ))}
                </Select>
            );
        case Switch:
            return <Switch {...elementProps} />;
        case Checkbox:
            return <Checkbox {...elementProps} />;
        case Radio:
            return <Radio {...elementProps} />;
        default:
            // 处理其他所有情况
            break;
    }
    let mainDom: React.ReactNode = <Input {...elementProps} />;

    // 如果element是React组件
    if (React.isValidElement(element)) {
        // 使用cloneElement，只传递props
        mainDom = React.cloneElement(element, elementProps);
    } else if (typeof element === 'function') {
        // 检查是否是渲染函数（通过参数长度判断）
        try {
            // 如果函数接受2个或更多参数，认为是渲染函数
            const renderFn = element as (
                createElement: typeof React.createElement,
                props: any,
            ) => React.ReactNode;
            mainDom = renderFn(React.createElement, elementProps);
        } catch (error) {
            // 如果出错，回退到使用createElement
            const Component = element as React.ComponentType<any>;
            mainDom = React.createElement(Component, elementProps);
        }
    }

    if (item.brotherNodes) {
        return (
            <>
                {mainDom}
                {typeof item.brotherNodes === 'function'
                    ? item.brotherNodes(React.createElement)
                    : item.brotherNodes}
            </>
        );
    }
    return mainDom;
}

/**
 * 创建预览元素
 */
export function createPreviewElement(item: FormConfig, params: any): React.ReactNode {
    const { field, preview } = item;
    const value = deepGet(params, field);

    if (!preview) {
        return <span>{value}</span>;
    }

    if (preview.element === 'img') {
        return (
            <img src={value} style={{ height: '80px', width: '80px', ...preview.style }} alt="" />
        );
    }

    let text = '';
    if (preview.formatter) {
        text = preview.formatter(params, item);
    } else if (item.previewFormatter) {
        text = item.previewFormatter(value, params, item);
    } else {
        text = value;
    }

    return isReactNode(text) ? text : <span>{text}</span>;
}
