import React, { useState, useRef } from 'react';
import { Card, Space, Switch, InputNumber, Button, Input, Select, DatePicker, Form } from 'antd';
import { Group } from 'antd/lib/radio';
import { BaseForm, defineTypedForm } from './index';
import type { FormConfig, BaseFormRef } from './types';

const Example: React.FC = () => {
    const formRef = useRef<BaseFormRef>(null);
    const [formData, setFormData] = useState({
        name: '',
        age: 18,
        email: '',
        gender: '',
        city: '',
        description: '',
        isActive: true,
    });
    const [disabled, setDisabled] = useState(false);
    const [isFilter, setIsFilter] = useState(false);
    const [maxCount, setMaxCount] = useState(3);

    // 在组件内部定义筛选表单配置和类型推导
    const userFormDefinition = defineTypedForm([
        {
            field: 'keyword',
            title: '关键词',
            element: Input,
            defaultValue: '', // 添加默认值以便类型推导
            props: {
                placeholder: '请输入关键词',
            },
        },
        {
            field: 'status',
            title: '状态',
            element: Select,
            defaultValue: '', // 添加默认值以便类型推导
            props: {
                placeholder: '请选择状态',
                options: [
                    { label: '全部', value: '' },
                    { label: '启用', value: 'active' },
                    { label: '禁用', value: 'inactive' },
                ],
            },
        },
        {
            field: 'createTime',
            title: '创建时间',
            element: <DatePicker.RangePicker placeholder={['开始时间2', '结束时间2']} />,
            defaultValue: undefined, // 日期范围默认为undefined
            props: {
                format: 'YYYY-MM-DD',
            },
        },
        {
            field: 'category',
            title: '分类',
            element: <Group />,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择分类',
                options: [
                    { label: '分类1', value: 'cat1' },
                    { label: '分类2', value: 'cat2' },
                    { label: '分类3', value: 'cat3' },
                ],
            },
        },
        {
            field: 'priority',
            title: '优先级',
            element: Select,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择优先级',
                options: [
                    { label: '高', value: 'high' },
                    { label: '中', value: 'medium' },
                    { label: '低', value: 'low' },
                ],
            },
        },
        {
            field: 'tags',
            title: '标签',
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请输入标签',
            },
        },
        {
            field: 'owner',
            title: '负责人',
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请输入负责人',
            },
        },
        {
            field: 'department',
            title: '部门',
            element: Select,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择部门',
                options: [
                    { label: '技术部', value: 'tech' },
                    { label: '产品部', value: 'product' },
                    { label: '运营部', value: 'operation' },
                ],
            },
        },
    ] as const);

    // 从定义中提取类型和配置
    const { FormData, createInitialValue, config: filterFormConfig } = userFormDefinition;
    const [filterData, setFilterData] = useState<typeof FormData>(createInitialValue());

    const finalFilterFormConfig = filterFormConfig.map((item) => {
        if (item.field === 'createTime') {
            return {
                ...item,
                show: () => filterData.status === 'active',
            };
        }
        return item;
    });

    // 基础表单配置
    const basicFormConfig: FormConfig[] = [
        {
            field: 'name',
            title: '姓名',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入姓名',
            },
            colProps: { span: 12 },
        },
        {
            field: 'isActive',
            title: '是否激活',
            element: Switch,
            colProps: { span: 12 },
        },
        {
            field: 'age',
            title: '年龄',
            element: InputNumber,
            props: {
                min: 0,
                max: 120,
                placeholder: '请输入年龄',
            },
            colProps: { span: 12 },
        },
        {
            field: 'email',
            title: '邮箱',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入邮箱',
            },
            rules: [
                {
                    type: 'email',
                    message: '请输入正确的邮箱格式',
                },
            ],
            show: (params) => params.isActive === true,
            colProps: { span: 12 },
        },
        {
            field: 'gender',
            title: '性别',
            element: Select,
            props: {
                placeholder: '请选择性别',
                options: [
                    { label: '男', value: 'male' },
                    { label: '女', value: 'female' },
                ],
            },
            colProps: { span: 12 },
            show: () => formData.isActive === true,
        },
        {
            field: 'city',
            title: '城市',
            element: Select,
            props: {
                placeholder: '请选择城市',
                options: [
                    { label: '北京', value: 'beijing' },
                    { label: '上海', value: 'shanghai' },
                    { label: '广州', value: 'guangzhou' },
                    { label: '深圳', value: 'shenzhen' },
                ],
            },
            colProps: { span: 12 },
        },
        {
            field: 'description',
            title: '描述',
            element: Input.TextArea,
            props: {
                placeholder: '请输入描述',
                rows: 4,
            },
            colProps: {
                span: 24,
            },
            itemProps: {
                labelCol: { span: 4 },
                wrapperCol: { span: 20 },
            },
        },
    ];
    const handleSubmit = (data: any) => {
        console.log('表单提交:', data);
    };

    const handleReset = () => {
        console.log('表单重置');
    };

    const handleLoadData = (params: any) => {
        console.log('加载数据:', params);
    };

    const handleValidate = async () => {
        try {
            await formRef.current?.validate();
            console.log('验证通过');
        } catch (error) {
            console.log('验证失败:', error);
        }
    };

    return (
        <div style={{ padding: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title="基础表单" size="small">
                    <BaseForm
                        ref={formRef}
                        config={basicFormConfig}
                        modelValue={formData}
                        onUpdateModelValue={setFormData}
                        disabled={disabled}
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                    />
                    <div style={{ marginTop: 16 }}>
                        <Button type="primary" onClick={handleValidate}>
                            验证表单
                        </Button>
                        <Button style={{ marginLeft: 8 }} onClick={() => formRef.current?.reset()}>
                            重置表单
                        </Button>
                    </div>
                </Card>

                <Card title="筛选表单" size="small">
                    <BaseForm
                        config={finalFilterFormConfig}
                        modelValue={filterData}
                        itemLayout={{ span: 6, gutter: 8 }}
                        onUpdateModelValue={setFilterData}
                        isFilter={isFilter}
                        filterMaxCount={maxCount}
                        onSubmit={handleSubmit}
                        onReset={handleReset}
                        onLoadData={handleLoadData}
                    />
                </Card>

                <Card title="控制面板" size="small">
                    <Space>
                        <span>禁用状态:</span>
                        <Switch checked={disabled} onChange={setDisabled} />

                        <span>筛选模式:</span>
                        <Switch checked={isFilter} onChange={setIsFilter} />

                        <span>最大显示数量:</span>
                        <InputNumber
                            min={1}
                            max={10}
                            value={maxCount}
                            onChange={(value) => setMaxCount(value || 3)}
                        />
                    </Space>
                </Card>

                {/* 标准Antd Form对比测试 */}
                <Card
                    title="🔍 标准Antd Form对比测试"
                    size="small"
                    style={{ border: '2px solid #52c41a' }}
                >
                    <div
                        style={{
                            marginBottom: 16,
                            padding: 16,
                            backgroundColor: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: 4,
                        }}
                    >
                        <h4 style={{ color: '#389e0d', margin: '0 0 8px 0' }}>
                            � 标准Antd Form测试：
                        </h4>
                        <p style={{ margin: 0, fontSize: 14 }}>
                            <strong>对比测试：</strong>使用标准的Antd Form实现，看看是否有焦点问题
                            <br />
                            <strong>请在两个区域都测试输入：</strong>
                        </p>
                    </div>

                    {/* 标准Antd Form */}
                    <div style={{ marginBottom: 24 }}>
                        <h5>标准Antd Form（对照组）：</h5>
                        <Form
                            layout="horizontal"
                            labelCol={{ span: 8 }}
                            wrapperCol={{ span: 16 }}
                            onValuesChange={(changedValues, allValues) => {
                                console.log('📋 标准Form变化:', changedValues, allValues);
                            }}
                        >
                            <Form.Item label="标准输入1" name="standardInput1">
                                <Input
                                    placeholder="标准Antd Form输入框1"
                                    onFocus={() => console.log('🎯 标准输入1 获得焦点')}
                                    onBlur={() => console.log('❌ 标准输入1 失去焦点')}
                                />
                            </Form.Item>
                            <Form.Item label="标准输入2" name="standardInput2">
                                <Input
                                    placeholder="标准Antd Form输入框2"
                                    onFocus={() => console.log('🎯 标准输入2 获得焦点')}
                                    onBlur={() => console.log('❌ 标准输入2 失去焦点')}
                                />
                            </Form.Item>
                        </Form>
                    </div>

                    {/* BaseForm */}
                    <div>
                        <h5>BaseForm（测试组）：</h5>
                        <BaseForm
                            config={[
                                {
                                    field: 'testInput1',
                                    title: 'BaseForm输入1',
                                    element: Input,
                                    props: {
                                        placeholder: 'BaseForm输入框1',
                                        onFocus: () => console.log('🎯 BaseForm输入1 获得焦点'),
                                        onBlur: () => console.log('❌ BaseForm输入1 失去焦点'),
                                    },
                                    colProps: { span: 12 },
                                },
                                {
                                    field: 'testInput2',
                                    title: 'BaseForm输入2',
                                    element: Input,
                                    props: {
                                        placeholder: 'BaseForm输入框2',
                                        onFocus: () => console.log('🎯 BaseForm输入2 获得焦点'),
                                        onBlur: () => console.log('❌ BaseForm输入2 失去焦点'),
                                    },
                                    colProps: { span: 12 },
                                },
                            ]}
                            modelValue={formData}
                            onUpdateModelValue={(newData) => {
                                console.log('🔄 BaseForm数据更新:', newData);
                                setFormData(newData);
                            }}
                            onItemChange={(field, value) => {
                                console.log('📝 BaseForm字段变化:', field, '=', value);
                            }}
                            labelCol={{ span: 8 }}
                            wrapperCol={{ span: 16 }}
                        />
                    </div>

                    <div
                        style={{
                            marginTop: 16,
                            padding: 12,
                            backgroundColor: '#fff7e6',
                            border: '1px solid #ffd591',
                            borderRadius: 4,
                        }}
                    >
                        <h5 style={{ margin: '0 0 8px 0', color: '#d46b08' }}>测试说明：</h5>
                        <ol style={{ margin: 0, paddingLeft: 20, fontSize: 13 }}>
                            <li>🔧 已使用React.memo优化BaseForm，防止不必要的重新渲染</li>
                            <li>先在"标准Antd Form"中测试连续输入，观察是否正常</li>
                            <li>再在"BaseForm"中测试连续输入，看看是否还有输入冻结问题</li>
                            <li>观察控制台日志，检查是否还有频繁的重新渲染</li>
                            <li>如果BaseForm现在可以正常连续输入，说明问题已解决</li>
                        </ol>
                    </div>

                    <div
                        style={{
                            marginTop: 16,
                            padding: 12,
                            backgroundColor: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: 4,
                        }}
                    >
                        <h5 style={{ margin: '0 0 8px 0', color: '#389e0d' }}>
                            当前BaseForm数据：
                        </h5>
                        <pre style={{ margin: 0, fontSize: 12, maxHeight: 100, overflow: 'auto' }}>
                            {JSON.stringify(formData, null, 2)}
                        </pre>
                    </div>
                </Card>

                <Card title="基础表单数据" size="small">
                    <pre>{JSON.stringify(formData, null, 2)}</pre>
                </Card>

                <Card title="筛选表单数据" size="small">
                    <pre>{JSON.stringify(filterData, null, 2)}</pre>
                </Card>
            </Space>
        </div>
    );
};

export default Example;
