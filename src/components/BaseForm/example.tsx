import React, { useState, useRef } from 'react';
import { Card, Space, Switch, InputNumber, Button, Input, Select, DatePicker } from 'antd';
import { Group } from 'antd/lib/radio';
import { BaseForm, defineTypedForm } from './index';
import type { FormConfig, BaseFormRef } from './types';

const Example: React.FC = () => {
    const formRef = useRef<BaseFormRef>(null);
    const [formData, setFormData] = useState({
        name: '',
        age: 18,
        email: '',
        gender: '',
        city: '',
        description: '',
        isActive: true,
    });
    const [disabled, setDisabled] = useState(false);
    const [isFilter, setIsFilter] = useState(false);
    const [maxCount, setMaxCount] = useState(3);

    // 在组件内部定义筛选表单配置和类型推导
    const userFormDefinition = defineTypedForm([
        {
            field: 'keyword',
            title: '关键词',
            element: Input,
            defaultValue: '', // 添加默认值以便类型推导
            props: {
                placeholder: '请输入关键词',
            },
        },
        {
            field: 'status',
            title: '状态',
            element: Select,
            defaultValue: '', // 添加默认值以便类型推导
            props: {
                placeholder: '请选择状态',
                options: [
                    { label: '全部', value: '' },
                    { label: '启用', value: 'active' },
                    { label: '禁用', value: 'inactive' },
                ],
            },
        },
        {
            field: 'createTime',
            title: '创建时间',
            element: <DatePicker.RangePicker placeholder={['开始时间2', '结束时间2']} />,
            defaultValue: undefined, // 日期范围默认为undefined
            props: {
                format: 'YYYY-MM-DD',
            },
        },
        {
            field: 'category',
            title: '分类',
            element: <Group />,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择分类',
                options: [
                    { label: '分类1', value: 'cat1' },
                    { label: '分类2', value: 'cat2' },
                    { label: '分类3', value: 'cat3' },
                ],
            },
        },
        {
            field: 'priority',
            title: '优先级',
            element: Select,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择优先级',
                options: [
                    { label: '高', value: 'high' },
                    { label: '中', value: 'medium' },
                    { label: '低', value: 'low' },
                ],
            },
        },
        {
            field: 'tags',
            title: '标签',
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请输入标签',
            },
        },
        {
            field: 'owner',
            title: '负责人',
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请输入负责人',
            },
        },
        {
            field: 'department',
            title: '部门',
            element: Select,
            defaultValue: '', // 添加默认值
            props: {
                placeholder: '请选择部门',
                options: [
                    { label: '技术部', value: 'tech' },
                    { label: '产品部', value: 'product' },
                    { label: '运营部', value: 'operation' },
                ],
            },
        },
    ] as const);

    // 从定义中提取类型和配置
    const { FormData, createInitialValue, config: filterFormConfig } = userFormDefinition;
    const [filterData, setFilterData] = useState<typeof FormData>(createInitialValue());

    const finalFilterFormConfig = filterFormConfig.map((item) => {
        if (item.field === 'createTime') {
            return {
                ...item,
                show: () => filterData.status === 'active',
            };
        }
        return item;
    });

    // 基础表单配置
    const basicFormConfig: FormConfig[] = [
        {
            field: 'name',
            title: '姓名',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入姓名',
            },
            colProps: { span: 12 },
        },
        {
            field: 'isActive',
            title: '是否激活',
            element: Switch,
            colProps: { span: 12 },
        },
        {
            field: 'age',
            title: '年龄',
            element: InputNumber,
            props: {
                min: 0,
                max: 120,
                placeholder: '请输入年龄',
            },
            colProps: { span: 12 },
        },
        {
            field: 'email',
            title: '邮箱',
            element: Input,
            required: 'check',
            props: {
                placeholder: '请输入邮箱',
            },
            rules: [
                {
                    type: 'email',
                    message: '请输入正确的邮箱格式',
                },
            ],
            show: (params) => params.isActive === true,
            colProps: { span: 12 },
        },
        {
            field: 'gender',
            title: '性别',
            element: Select,
            props: {
                placeholder: '请选择性别',
                options: [
                    { label: '男', value: 'male' },
                    { label: '女', value: 'female' },
                ],
            },
            colProps: { span: 12 },
            show: () => formData.isActive === true,
        },
        {
            field: 'city',
            title: '城市',
            element: Select,
            props: {
                placeholder: '请选择城市',
                options: [
                    { label: '北京', value: 'beijing' },
                    { label: '上海', value: 'shanghai' },
                    { label: '广州', value: 'guangzhou' },
                    { label: '深圳', value: 'shenzhen' },
                ],
            },
            colProps: { span: 12 },
        },
        {
            field: 'description',
            title: '描述',
            element: Input.TextArea,
            props: {
                placeholder: '请输入描述',
                rows: 4,
            },
            colProps: {
                span: 24,
            },
            itemProps: {
                labelCol: { span: 4 },
                wrapperCol: { span: 20 },
            },
        },
    ];
    const handleSubmit = (data: any) => {
        console.log('表单提交:', data);
    };

    const handleReset = () => {
        console.log('表单重置');
    };

    const handleLoadData = (params: any) => {
        console.log('加载数据:', params);
    };

    const handleValidate = async () => {
        try {
            await formRef.current?.validate();
            console.log('验证通过');
        } catch (error) {
            console.log('验证失败:', error);
        }
    };

    return (
        <div style={{ padding: 24 }}>
            <Space direction="vertical" size="large" style={{ width: '100%' }}>
                <Card title="基础表单" size="small">
                    <BaseForm
                        ref={formRef}
                        config={basicFormConfig}
                        modelValue={formData}
                        onUpdateModelValue={setFormData}
                        disabled={disabled}
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                    />
                    <div style={{ marginTop: 16 }}>
                        <Button type="primary" onClick={handleValidate}>
                            验证表单
                        </Button>
                        <Button style={{ marginLeft: 8 }} onClick={() => formRef.current?.reset()}>
                            重置表单
                        </Button>
                    </div>
                </Card>

                <Card title="筛选表单" size="small">
                    <BaseForm
                        config={finalFilterFormConfig}
                        modelValue={filterData}
                        itemLayout={{ span: 6, gutter: 8 }}
                        onUpdateModelValue={setFilterData}
                        isFilter={isFilter}
                        filterMaxCount={maxCount}
                        onSubmit={handleSubmit}
                        onReset={handleReset}
                        onLoadData={handleLoadData}
                    />
                </Card>

                <Card title="控制面板" size="small">
                    <Space>
                        <span>禁用状态:</span>
                        <Switch checked={disabled} onChange={setDisabled} />

                        <span>筛选模式:</span>
                        <Switch checked={isFilter} onChange={setIsFilter} />

                        <span>最大显示数量:</span>
                        <InputNumber
                            min={1}
                            max={10}
                            value={maxCount}
                            onChange={(value) => setMaxCount(value || 3)}
                        />
                    </Space>
                </Card>

                {/* 焦点问题诊断测试区域 */}
                <Card
                    title="🔍 焦点问题诊断测试"
                    size="small"
                    style={{ border: '2px solid #ff4d4f' }}
                >
                    <div
                        style={{
                            marginBottom: 16,
                            padding: 16,
                            backgroundColor: '#fff2f0',
                            border: '1px solid #ffccc7',
                            borderRadius: 4,
                        }}
                    >
                        <h4 style={{ color: '#cf1322', margin: '0 0 8px 0' }}>
                            🔧 终极解决方案测试 v3：
                        </h4>
                        <p style={{ margin: 0, fontSize: 14 }}>
                            <strong>终极方案：</strong>
                            完全使用Form的name方式进行数据控制，移除所有手动value/onChange
                            <br />
                            <strong>请测试以下操作并反馈结果：</strong>
                        </p>
                        <ol style={{ margin: '8px 0 0 0', paddingLeft: 20, fontSize: 14 }}>
                            <li>
                                <strong>连续输入测试：</strong>在"测试输入1"中连续输入"hello world"
                            </li>
                            <li>
                                <strong>数字输入测试：</strong>在"测试输入2"中连续输入"123456789"
                            </li>
                            <li>
                                <strong>焦点保持测试：</strong>输入过程中是否保持焦点
                            </li>
                            <li>
                                <strong>控制台观察：</strong>查看是否还有频繁的重新渲染日志
                            </li>
                            <li>
                                <strong>数据同步测试：</strong>检查下方的表单数据是否正确更新
                            </li>
                        </ol>
                        <div
                            style={{
                                marginTop: 8,
                                padding: 8,
                                backgroundColor: '#e6f7ff',
                                borderRadius: 4,
                            }}
                        >
                            <strong>终极解决方案说明：</strong>
                            <ul style={{ margin: '4px 0 0 0', paddingLeft: 16, fontSize: 13 }}>
                                <li>🔥 完全移除手动value和onChange传递</li>
                                <li>🔥 移除Form的initialValues，避免初始化冲突</li>
                                <li>🔥 使用最纯粹的Form.Item name属性控制</li>
                                <li>🔥 createPureFormElement只创建纯组件</li>
                                <li>🔥 只在初始化时设置一次表单值</li>
                                <li>🔥 完全依赖Antd Form的内部机制</li>
                            </ul>
                        </div>
                    </div>

                    <BaseForm
                        config={[
                            {
                                field: 'testInput1',
                                title: '测试输入1',
                                element: Input,
                                props: {
                                    placeholder: '请连续输入多个字符测试焦点',
                                    onFocus: () => console.log('🎯 testInput1 获得焦点'),
                                    onBlur: () => console.log('❌ testInput1 失去焦点'),
                                },
                                colProps: { span: 12 },
                            },
                            {
                                field: 'testInput2',
                                title: '测试输入2',
                                element: Input,
                                props: {
                                    placeholder: '请连续输入数字测试焦点',
                                    onFocus: () => console.log('🎯 testInput2 获得焦点'),
                                    onBlur: () => console.log('❌ testInput2 失去焦点'),
                                },
                                colProps: { span: 12 },
                            },
                        ]}
                        modelValue={formData}
                        onUpdateModelValue={(newData) => {
                            console.log('🔄 BaseForm数据更新:', newData);
                            setFormData(newData);
                        }}
                        onItemChange={(field, value) => {
                            console.log('📝 字段变化:', field, '=', value);
                        }}
                    />

                    <div
                        style={{
                            marginTop: 16,
                            padding: 12,
                            backgroundColor: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: 4,
                        }}
                    >
                        <h5 style={{ margin: '0 0 8px 0', color: '#389e0d' }}>当前表单数据：</h5>
                        <pre style={{ margin: 0, fontSize: 12, maxHeight: 100, overflow: 'auto' }}>
                            {JSON.stringify(formData, null, 2)}
                        </pre>
                    </div>
                </Card>

                <Card title="基础表单数据" size="small">
                    <pre>{JSON.stringify(formData, null, 2)}</pre>
                </Card>

                <Card title="筛选表单数据" size="small">
                    <pre>{JSON.stringify(filterData, null, 2)}</pre>
                </Card>
            </Space>
        </div>
    );
};

export default Example;
