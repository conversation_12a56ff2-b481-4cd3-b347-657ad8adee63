.base-form {
  // 确保基础表单样式，防止页面刷新时丢失
//   &.ant-form {
//     font-size: 14px;
//     line-height: 1.5715;
//     color: rgba(0, 0, 0, 0.85);
//     box-sizing: border-box;
//     margin: 0;
//     padding: 0;
//     list-style: none;
//   }

//   // 修复表单项基础样式
//   .ant-form-item {
//     margin-bottom: 24px;
//     vertical-align: top;

//     &-label {
//       position: relative;
//       display: inline-block;
//       overflow: hidden;
//       white-space: nowrap;
//       text-align: right;
//       vertical-align: middle;

//       > label {
//         position: relative;
//         display: inline-flex;
//         align-items: center;
//         max-width: 100%;
//         height: 32px;
//         color: rgba(0, 0, 0, 0.85);
//         font-size: 14px;

//         &.ant-form-item-required:not(.ant-form-item-no-asterisk)::before {
//           display: inline-block;
//           margin-right: 4px;
//           color: #ff4d4f;
//           font-size: 14px;
//           font-family: SimSun, sans-serif;
//           line-height: 1;
//           content: '*';
//         }
//       }
//     }

//     &-control {
//       display: flex;
//       flex-direction: column;
//       flex: 1;
//       min-width: 0;

//       &-input {
//         position: relative;
//         display: flex;
//         align-items: center;
//         min-height: 32px;

//         &-content {
//           flex: auto;
//           max-width: 100%;
//         }
//       }
//     }
//   }

//   // 修复输入框样式
//   .ant-input {
//     position: relative;
//     display: inline-block;
//     width: 100%;
//     min-width: 0;
//     padding: 4px 11px;
//     color: rgba(0, 0, 0, 0.85);
//     font-size: 14px;
//     line-height: 1.5715;
//     background-color: #fff;
//     background-image: none;
//     border: 1px solid #d9d9d9;
//     border-radius: 6px;
//     transition: all 0.3s;

//     &:hover {
//       border-color: #40a9ff;
//       border-right-width: 1px;
//     }

//     &:focus,
//     &-focused {
//       border-color: #40a9ff;
//       box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
//       border-right-width: 1px;
//       outline: 0;
//     }
//   }

//   // 修复选择器样式
//   .ant-select {
//     position: relative;
//     display: inline-block;
//     cursor: pointer;

//     &:not(.ant-select-customize-input) .ant-select-selector {
//       position: relative;
//       background-color: #fff;
//       border: 1px solid #d9d9d9;
//       border-radius: 6px;
//       transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
//     }

//     &-single .ant-select-selector {
//       display: flex;
//       align-items: center;
//       padding: 0 11px;
//       height: 32px;
//     }
//   }
  &-flex {
    display: flex;

    & > div {
      width: 100%;
    }
  }

  .form-item-row {
    width: 100%;
    flex-wrap: wrap;

    > div {
      height: 100%;
      padding-right: 8px;
    }

    .form-item-row-search {
      padding-right: 0 !important;
    }
  }

  .ant-col-24.ant-form-item-label {
    line-height: 39.999px;
  }

  .base-form-flex-item {
    .ant-form-item-label {
      padding: 0;
    }

    .ant-form-item-control {
      line-height: initial;
    }

    &.ant-form-item-required:not(.ant-form-item-no-asterisk) {
      .base-form-flex-label {
        span {
          position: relative;

          &::before {
            content: '*';
            color: #ff4d4f;
            position: absolute;
            left: -10px;
            top: 0;
          }
        }
      }
    }

    .base-form-flex-label {
      text-align: right;
      padding: 0 5px 0 10px;
    }

    .base-form-flex-content {
      padding: 0;
    }

    .base-form-flex-error {
      color: #ff4d4f;
      font-size: 12px;
      line-height: 1;
      padding-top: 2px;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  &.ant-form-inline {
    .ant-form-item-label {
      padding: 0;
    }
  }

  &-filter {
    .form-item-row-search {
      display: flex;
      align-items: flex-end;

      .ant-form-item {
        margin-bottom: 0;
      }
    }
  }
}
