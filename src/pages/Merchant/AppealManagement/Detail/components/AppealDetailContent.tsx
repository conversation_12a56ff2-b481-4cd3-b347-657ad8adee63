import React from 'react';
import {
    Descriptions,
    Spin,
    Tag,
    Table,
    Typography,
    Image,
    Alert,
    Divider,
    Row,
    Col,
    Statistic,
    Button,
    Space,
} from 'antd';
import { ClockCircleOutlined, UserOutlined, ShopOutlined } from '@ant-design/icons';
import moment from 'moment';
import ProCard from '@ant-design/pro-card';
import commonStyles from '@/assets/styles/common.less';
import { mockAppealDetail } from '../mockData';
import TextArea from 'antd/lib/input/TextArea';

const { Title, Text, Paragraph } = Typography;

interface AppealDetailContentProps {
    appealDetail?: API.AppealDetail;
    loading?: boolean;
    onRefresh?: () => void;
}

const AppealDetailContent: React.FC<AppealDetailContentProps> = ({
    appealDetail: propAppealDetail,
    loading,
    onRefresh,
}) => {
    // 使用mock数据，如果没有传入数据的话
    const appealDetail = propAppealDetail || mockAppealDetail;

    // 获取状态标签颜色
    const getStatusTagColor = (status: string) => {
        const statusColorMap: Record<string, string> = {
            '01': 'orange', // 待处理
            '02': 'blue', // 处理中
            '03': 'orange', // 待商家处理
            '04': 'blue', // 商家处理中
            '05': 'green', // 商家通过
            '06': 'red', // 商家拒绝
            '07': 'volcano', // 超时
            '08': 'default', // 已关闭
            '09': 'purple', // 二次申诉
        };
        return statusColorMap[status] || 'default';
    };

    // 获取优先级标签
    const getPriorityTag = (priority: string) => {
        const priorityMap: Record<string, { text: string; color: string }> = {
            '01': { text: '低', color: 'default' },
            '02': { text: '中', color: 'orange' },
            '03': { text: '高', color: 'red' },
            '04': { text: '紧急', color: 'volcano' },
        };
        return priorityMap[priority] || { text: '未知', color: 'default' };
    };

    const orderInfo = {
        listData: [],
        columns: [
            {
                title: '充电量（度）',
                dataIndex: 'chargingCapacity',
            },
            {
                title: '充电时长（分钟）',
                dataIndex: 'chargingDuration',
            },
            {
                title: '开始充电时间',
                dataIndex: 'startTime',
            },
            {
                title: '结束充电时间',
                dataIndex: 'endTime',
            },
            {
                title: '支付完成时间',
                dataIndex: 'payDoneTime',
            },
        ],
    };

    if (loading) {
        return <Spin spinning style={{ minHeight: '400px' }} />;
    }

    return (
        <>
            <Row gutter={12}>
                {/* 左侧状态信息 */}
                <Col span={16}>
                    <Title level={3} style={{ marginBottom: '16px' }}>
                        {appealDetail?.appealStatusName || '-'}
                        <Tag
                            style={{ marginLeft: '8px' }}
                            color={getStatusTagColor(appealDetail?.appealStatus || '')}
                        >
                            未赔付
                        </Tag>
                    </Title>
                </Col>
                {/* 右侧操作 */}
                <Col span={8}>
                    {/* 操作区域 */}
                    <Space
                        direction="horizontal"
                        style={{ width: '100%', justifyContent: 'flex-end' }}
                    >
                        <Button type="primary" block onClick={onRefresh}>
                            平台赔付
                        </Button>
                        <Button danger block>
                            商家赔付
                        </Button>
                        <Button block>无需赔付</Button>
                    </Space>
                </Col>
            </Row>
            <div
                style={{
                    marginBottom: '16px',
                    padding: '16px',
                    backgroundColor: '#fafafa',
                    borderRadius: '6px',
                }}
            >
                {/* 申诉信息 */}
                <Title
                    level={4}
                    style={{ margin: '16px 0 8px' }}
                    className={commonStyles['form-title']}
                >
                    申诉信息
                </Title>
                <Row gutter={16}>
                    <Col span={12}>
                        <Statistic
                            title="订单车牌号"
                            value={appealDetail?.licensePlateNumber || '-'}
                            valueStyle={{ fontSize: '16px', fontWeight: 'bold' }}
                        />
                    </Col>

                    <Col span={12}>
                        <div style={{ marginTop: '4px' }}>
                            <ClockCircleOutlined
                                style={{
                                    fontSize: '16px',
                                    color: '#1890ff',
                                    marginRight: '4px',
                                }}
                            />
                            <Text type="secondary">申诉时间</Text>
                            <br />
                            <Text strong>
                                {appealDetail?.appealTime
                                    ? moment(appealDetail.appealTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'}
                            </Text>
                        </div>
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title="申诉金额"
                            value={appealDetail?.appealAmount || 0}
                            precision={2}
                            prefix="¥"
                            valueStyle={{ color: '#cf1322' }}
                        />
                    </Col>
                    <Col span={12}>
                        <div style={{ marginTop: '4px' }}>
                            <ClockCircleOutlined
                                style={{
                                    fontSize: '16px',
                                    color: '#70c3af',
                                    marginRight: '4px',
                                }}
                            />
                            <Text type="secondary">处理时间</Text>
                            <br />
                            <Text strong>
                                {appealDetail?.handleTime
                                    ? moment(appealDetail.handleTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '未处理'}
                            </Text>
                        </div>
                    </Col>

                    <Col span={12}>
                        <div style={{ marginBottom: '16px' }}>
                            <Text type="secondary">申诉详情</Text>
                            <Paragraph
                                style={{
                                    backgroundColor: '#fafafa',
                                    padding: '12px',
                                    borderRadius: '6px',
                                    border: '1px solid #d9d9d9',
                                }}
                            >
                                {appealDetail?.appealContent || '-'}
                            </Paragraph>
                        </div>{' '}
                    </Col>

                    <Col span={24}>
                        {appealDetail?.attachments && appealDetail.attachments.length > 0 && (
                            <div>
                                <Text type="secondary">相关附件</Text>
                                <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
                                    {appealDetail.attachments.map((attachment, index) => (
                                        <div key={index} style={{ textAlign: 'center' }}>
                                            <Image
                                                width={120}
                                                height={120}
                                                src={attachment.fileUrl}
                                                alt={attachment.fileName}
                                                style={{
                                                    objectFit: 'cover',
                                                    borderRadius: '6px',
                                                    border: '1px solid #d9d9d9',
                                                }}
                                            />
                                            <div
                                                style={{
                                                    marginTop: '4px',
                                                    fontSize: '12px',
                                                    color: '#666',
                                                    maxWidth: '120px',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    whiteSpace: 'nowrap',
                                                }}
                                            >
                                                {attachment.fileName}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}{' '}
                    </Col>
                </Row>

                {/* 订单信息 */}
                <Title
                    level={4}
                    style={{ margin: '16px 0 8px' }}
                    className={commonStyles['form-title']}
                >
                    订单信息
                    <a href="" style={{ fontSize: '14px', marginLeft: '4px' }}>
                        查看更多&gt;
                    </a>
                </Title>
                <Table
                    dataSource={orderInfo.listData}
                    columns={orderInfo.columns}
                    bordered
                    style={{ marginBottom: '8px' }}
                    size="small"
                ></Table>
                <Descriptions column={3} bordered size="small" labelStyle={{ fontSize: '16px' }}>
                    <Descriptions.Item label="停车费说明(外显)" span={3}>
                        {
                            '车牌识别限时免费停车，在特来电APP进行爱车认证，充电可免2小时停车费，超时以场地实际收费为准'
                        }
                    </Descriptions.Item>
                    <Descriptions.Item label="运营商名称" span={1}>
                        {appealDetail?.operatorName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="场站名称" span={2}>
                        {appealDetail?.stationName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="申诉时订单状态">
                        {appealDetail?.appealStatusName || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="订单号">
                        {appealDetail?.orderNo || '-'}
                    </Descriptions.Item>
                    <Descriptions.Item label="第三方订单号">
                        {appealDetail?.orderNo || '-'}
                    </Descriptions.Item>
                </Descriptions>

                {/* 处理记录 */}
                <Title
                    level={4}
                    style={{ margin: '16px 0 8px' }}
                    className={commonStyles['form-title']}
                >
                    处理记录
                </Title>
                <Table
                    dataSource={orderInfo.listData}
                    columns={orderInfo.columns}
                    bordered
                    style={{ marginBottom: '8px' }}
                    size="small"
                ></Table>

                {/* 处理结果 */}
                {/* {appealDetail?.handleResult && (
                    <div
                        style={{
                            marginBottom: '16px',
                            padding: '16px',
                            backgroundColor: '#fff',
                            border: '1px solid #d9d9d9',
                            borderRadius: '6px',
                        }}
                    >
                        <Title level={4} style={{ marginBottom: '16px' }}>
                            处理结果
                        </Title>
                        <Alert
                            message="处理完成"
                            description={appealDetail.handleRemark}
                            type="success"
                            showIcon
                            style={{ marginBottom: '16px' }}
                        />
                        <Descriptions column={2} bordered>
                            <Descriptions.Item label="处理结果">
                                <Tag color="green">{appealDetail.handleResult}</Tag>
                            </Descriptions.Item>
                            <Descriptions.Item label="处理人">
                                {appealDetail.handlePerson || '-'}
                            </Descriptions.Item>
                            <Descriptions.Item label="处理时间" span={2}>
                                {appealDetail.handleTime
                                    ? moment(appealDetail.handleTime).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'}
                            </Descriptions.Item>
                            <Descriptions.Item label="处理说明" span={2}>
                                {appealDetail.handleRemark || '-'}
                            </Descriptions.Item>
                        </Descriptions>
                    </div>
                )} */}
            </div>
        </>
    );
};

export default AppealDetailContent;
