// Mock数据用于申诉列表页面测试
export const mockAppealList: API.AppealListItem[] = [
    {
        id: '1',
        appealNo: 'AP202312150001',
        licensePlateNumber: '粤A88888',
        userPhone: '138****8888',
        merchantId: 'M001',
        appealType: '01',
        appealTypeName: '停车费',
        appealStatus: '03',
        appealStatusName: '待商家处理',
        priority: '02',
        priorityName: '中',
        appealTitle: '停车费收费异常申诉',
        appealContent:
            '我在贵停车场停车2小时，但是被收取了全天费用，明显不合理。我有停车小票和支付记录作为证据，希望能够退还多收的费用。',
        appealAmount: 50.0,
        orderNo: 'ORD202312150001',
        stationId: 'ST001',
        stationName: '万达广场停车场',
        operatorName: '万达物业',
        cityName: '北京市',
        appealTime: '2023-12-15 10:30:00',
        handleTime: '',
        handlePerson: '',
        handleResult: '',
        handleRemark: '',
        deadlineTime: '2023-12-17 10:30:00',
        createTime: '2023-12-15 10:30:00',
        updateTime: '2023-12-15 10:30:00',
        compensationForm: '01', // 退款
        compensationStatus: '01', // 未赔付
        isSame: '01', // 是
    },
    {
        id: '2',
        appealNo: 'AP202312140001',
        licensePlateNumber: '京B12345',
        userPhone: '139****9999',
        merchantId: 'M002',
        appealType: '02',
        appealTypeName: '占位费',
        appealStatus: '05',
        appealStatusName: '商家通过',
        priority: '01',
        priorityName: '高',
        appealTitle: '占位费误收申诉',
        appealContent: '我的车辆已经离开停车位，但是系统仍然在收取占位费，请核实并退还误收费用。',
        appealAmount: 30.0,
        orderNo: 'ORD202312140001',
        stationId: 'ST002',
        stationName: '银泰中心停车场',
        operatorName: '银泰物业',
        cityName: '上海市',
        appealTime: '2023-12-14 14:20:00',
        handleTime: '2023-12-14 16:30:00',
        handlePerson: '李四',
        handleResult: '申诉成功',
        handleRemark: '经核实，确实存在系统异常，已安排退款',
        deadlineTime: '2023-12-16 14:20:00',
        createTime: '2023-12-14 14:20:00',
        updateTime: '2023-12-14 16:30:00',
        compensationForm: '01', // 退款
        compensationStatus: '02', // 已赔付
        isSame: '01', // 是
    },
    {
        id: '3',
        appealNo: 'AP202312130001',
        licensePlateNumber: '沪C67890',
        userPhone: '137****7777',
        merchantId: 'M003',
        appealType: '01',
        appealTypeName: '停车费',
        appealStatus: '06',
        appealStatusName: '商家拒绝',
        priority: '03',
        priorityName: '低',
        appealTitle: '停车费计费争议',
        appealContent: '认为停车费计算有误，希望重新核算费用。',
        appealAmount: 25.0,
        orderNo: 'ORD202312130001',
        stationId: 'ST003',
        stationName: '华润万象城停车场',
        operatorName: '华润物业',
        cityName: '深圳市',
        appealTime: '2023-12-13 09:15:00',
        handleTime: '2023-12-13 15:45:00',
        handlePerson: '王五',
        handleResult: '申诉驳回',
        handleRemark: '经核实，计费正确，无误收情况',
        deadlineTime: '2023-12-15 09:15:00',
        createTime: '2023-12-13 09:15:00',
        updateTime: '2023-12-13 15:45:00',
        compensationForm: '02', // 优惠券
        compensationStatus: '01', // 未赔付
        isSame: '02', // 否
    },
    {
        id: '4',
        appealNo: 'AP202312120001',
        licensePlateNumber: '浙A11111',
        userPhone: '136****6666',
        merchantId: 'M004',
        appealType: '02',
        appealTypeName: '占位费',
        appealStatus: '04',
        appealStatusName: '超时未处理',
        priority: '01',
        priorityName: '高',
        appealTitle: '占位费异常扣费申诉',
        appealContent: '车辆正常停放，但被异常收取占位费，请尽快处理。',
        appealAmount: 80.0,
        orderNo: 'ORD202312120001',
        stationId: 'ST004',
        stationName: '龙湖天街停车场',
        operatorName: '龙湖物业',
        cityName: '杭州市',
        appealTime: '2023-12-12 11:00:00',
        handleTime: '',
        handlePerson: '',
        handleResult: '',
        handleRemark: '',
        deadlineTime: '2023-12-14 11:00:00',
        createTime: '2023-12-12 11:00:00',
        updateTime: '2023-12-12 11:00:00',
        compensationForm: '01', // 退款
        compensationStatus: '01', // 未赔付
        isSame: '01', // 是
    },
    {
        id: '5',
        appealNo: 'AP202312110001',
        licensePlateNumber: '苏A22222',
        userPhone: '135****5555',
        merchantId: 'M005',
        appealType: '01',
        appealTypeName: '停车费',
        appealStatus: '08',
        appealStatusName: '已结束',
        priority: '02',
        priorityName: '中',
        appealTitle: '停车费重复扣费申诉',
        appealContent: '同一次停车被重复扣费，请退还多收费用。',
        appealAmount: 40.0,
        orderNo: 'ORD202312110001',
        stationId: 'ST005',
        stationName: '凯德广场停车场',
        operatorName: '凯德物业',
        cityName: '南京市',
        appealTime: '2023-12-11 16:30:00',
        handleTime: '2023-12-11 18:00:00',
        handlePerson: '赵六',
        handleResult: '申诉成功',
        handleRemark: '确认重复扣费，已退款处理',
        deadlineTime: '2023-12-13 16:30:00',
        createTime: '2023-12-11 16:30:00',
        updateTime: '2023-12-11 18:00:00',
        compensationForm: '01', // 退款
        compensationStatus: '02', // 已赔付
        isSame: '01', // 是
    },
    {
        id: '6',
        appealNo: 'AP202312100001',
        licensePlateNumber: '川A33333',
        userPhone: '134****4444',
        merchantId: 'M006',
        appealType: '02',
        appealTypeName: '占位费',
        appealStatus: '02',
        appealStatusName: '待平台审核',
        priority: '02',
        priorityName: '中',
        appealTitle: '占位费计算错误申诉',
        appealContent: '占位费计算时间有误，实际占位时间较短。',
        appealAmount: 60.0,
        orderNo: 'ORD202312100001',
        stationId: 'ST006',
        stationName: '太古里停车场',
        operatorName: '太古物业',
        cityName: '成都市',
        appealTime: '2023-12-10 13:45:00',
        handleTime: '',
        handlePerson: '',
        handleResult: '',
        handleRemark: '',
        deadlineTime: '2023-12-12 13:45:00',
        createTime: '2023-12-10 13:45:00',
        updateTime: '2023-12-10 13:45:00',
        compensationForm: '03', // 线下赔付
        compensationStatus: '01', // 未赔付
        isSame: '02', // 否
    },
    {
        id: '7',
        appealNo: 'AP202312090001',
        licensePlateNumber: '鄂A44444',
        userPhone: '133****3333',
        merchantId: 'M007',
        appealType: '01',
        appealTypeName: '停车费',
        appealStatus: '07',
        appealStatusName: '系统处理',
        priority: '03',
        priorityName: '低',
        appealTitle: '停车费系统故障申诉',
        appealContent: '系统故障导致费用计算错误，请核实处理。',
        appealAmount: 35.0,
        orderNo: 'ORD202312090001',
        stationId: 'ST007',
        stationName: '国际金融中心停车场',
        operatorName: 'IFC物业',
        cityName: '武汉市',
        appealTime: '2023-12-09 10:20:00',
        handleTime: '2023-12-09 12:00:00',
        handlePerson: '系统',
        handleResult: '系统自动处理',
        handleRemark: '系统检测到异常，自动退款',
        deadlineTime: '2023-12-11 10:20:00',
        createTime: '2023-12-09 10:20:00',
        updateTime: '2023-12-09 12:00:00',
        compensationForm: '01', // 退款
        compensationStatus: '02', // 已赔付
        isSame: '01', // 是
    },
    {
        id: '8',
        appealNo: 'AP202312080001',
        licensePlateNumber: '湘A55555',
        userPhone: '132****2222',
        merchantId: 'M008',
        appealType: '02',
        appealTypeName: '占位费',
        appealStatus: '01',
        appealStatusName: '二次申诉',
        priority: '01',
        priorityName: '高',
        appealTitle: '占位费二次申诉',
        appealContent: '对之前的处理结果不满意，申请二次审核。',
        appealAmount: 90.0,
        orderNo: 'ORD202312080001',
        stationId: 'ST008',
        stationName: '步步高广场停车场',
        operatorName: '步步高物业',
        cityName: '长沙市',
        appealTime: '2023-12-08 15:10:00',
        handleTime: '',
        handlePerson: '',
        handleResult: '',
        handleRemark: '',
        deadlineTime: '2023-12-10 15:10:00',
        createTime: '2023-12-08 15:10:00',
        updateTime: '2023-12-08 15:10:00',
        compensationForm: '02', // 优惠券
        compensationStatus: '01', // 未赔付
        isSame: '01', // 是
    },
    {
        id: '9',
        appealNo: 'AP202312070001',
        licensePlateNumber: '闽A66666',
        userPhone: '131****1111',
        merchantId: 'M009',
        appealType: '01',
        appealTypeName: '停车费',
        appealStatus: '09',
        appealStatusName: '平台拒绝',
        priority: '02',
        priorityName: '中',
        appealTitle: '停车费争议申诉',
        appealContent: '对停车费收费标准有异议，申请核实。',
        appealAmount: 20.0,
        orderNo: 'ORD202312070001',
        stationId: 'ST009',
        stationName: '万达广场停车场',
        operatorName: '万达物业',
        cityName: '福州市',
        appealTime: '2023-12-07 12:30:00',
        handleTime: '2023-12-07 17:00:00',
        handlePerson: '平台客服',
        handleResult: '申诉驳回',
        handleRemark: '收费标准符合规定，申诉不成立',
        deadlineTime: '2023-12-09 12:30:00',
        createTime: '2023-12-07 12:30:00',
        updateTime: '2023-12-07 17:00:00',
        compensationForm: '01', // 退款
        compensationStatus: '01', // 未赔付
        isSame: '02', // 否
    },
    {
        id: '10',
        appealNo: 'AP202312060001',
        licensePlateNumber: '赣A77777',
        userPhone: '130****0000',
        merchantId: 'M010',
        appealType: '02',
        appealTypeName: '占位费',
        appealStatus: '03',
        appealStatusName: '待商家处理',
        priority: '03',
        priorityName: '低',
        appealTitle: '占位费收费疑问',
        appealContent: '对占位费的收费时间段有疑问，请核实。',
        appealAmount: 15.0,
        orderNo: 'ORD202312060001',
        stationId: 'ST010',
        stationName: '红谷滩万达停车场',
        operatorName: '万达物业',
        cityName: '南昌市',
        appealTime: '2023-12-06 08:45:00',
        handleTime: '',
        handlePerson: '',
        handleResult: '',
        handleRemark: '',
        deadlineTime: '2023-12-08 08:45:00',
        createTime: '2023-12-06 08:45:00',
        updateTime: '2023-12-06 08:45:00',
        compensationForm: '03', // 线下赔付
        compensationStatus: '01', // 未赔付
        isSame: '01', // 是
    },
];

// 分页mock数据响应
export const mockAppealListResponse = (params: any = {}) => {
    const { pageIndex = 1, pageSize = 10 } = params;
    const start = (pageIndex - 1) * pageSize;
    const end = start + pageSize;
    const records = mockAppealList.slice(start, end);

    return {
        code: '200',
        data: {
            pageIndex,
            pageSize,
            total: mockAppealList.length,
            records,
        },
        msg: '成功',
        ret: 0,
    };
};
