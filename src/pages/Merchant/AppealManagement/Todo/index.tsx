import { Layout, Tabs, Pagination, Radio, List } from 'antd';
import AppealDetailContent from '../Detail/components/AppealDetailContent';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { ProCard } from '@ant-design/pro-components';
const AppealListPage = () => {
    return (
        <PageHeaderWrapper>
            <ProCard>
                <Radio.Group defaultValue="a" buttonStyle="solid">
                    <Radio.Button value="a"> 待平台审核</Radio.Button>
                    <Radio.Button value="b"> 二次申诉</Radio.Button>
                    <Radio.Button value="c"> 超时未处理</Radio.Button>
                    <Radio.Button value="d"> 待商家处理</Radio.Button>
                </Radio.Group>

                <Layout
                    style={{
                        border: '1px solid #eee',
                        backgroundColor: '#fff',
                        marginTop: '16px',
                        height: '980px',
                        overflow: 'hidden',
                    }}
                >
                    <Layout.Sider
                        style={{
                            backgroundColor: '#fff',
                            borderRight: '1px solid #eee',
                        }}
                    >
                        <List
                            size="large"
                            itemLayout="horizontal"
                            dataSource={Array.from({ length: 12 }).map((_, index) => ({
                                title: '闽DDP0' + (85 + index),
                                dateTime: '2022-01-01 10:00:00',
                            }))}
                            renderItem={(item, index) => (
                                <List.Item
                                    key={index}
                                    style={{
                                        cursor: 'pointer',
                                    }}
                                >
                                    <List.Item.Meta
                                        title={item.title}
                                        description={item.dateTime}
                                    />
                                </List.Item>
                            )}
                        />
                    </Layout.Sider>
                    <Layout.Content
                        style={{
                            backgroundColor: '#fff',
                            padding: '16px',
                        }}
                    >
                        <AppealDetailContent />
                    </Layout.Content>
                </Layout>
                <Pagination
                    style={{
                        float: 'right',
                        marginTop: '16px',
                    }}
                />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AppealListPage;
