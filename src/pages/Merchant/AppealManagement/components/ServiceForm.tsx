import {
    ProForm,
    ProFormDependency,
    ProFormGroup,
    ProFormRadio,
    ProFormSelect,
} from '@ant-design/pro-components';
import { ProFormDigit, ProFormItem, ProFormText } from '@ant-design/pro-form';
import type { ProFormInstance } from '@ant-design/pro-form';
import { useRequest } from 'ahooks';
import { Button, Col, Input, InputNumber, Row, Space } from 'antd';
import React, { useEffect, useRef } from 'react';
import { history, useModel } from 'umi';

import UpLoadImgItem from '@/components/UpLoadImg/UpLoadImgCustom';
import QuillRichInput from '@/components/QuillRichInput';
import ServiceTag from './ServiceTag';
import { ConfigTypeEnum } from '@/constants/ServiceMarket';
import { isEmpty } from '@/utils/utils';
import {
    getServiceFinanceConfigListApi,
    getServiceTypeListApi,
} from '@/services/Merchant/ServiceMakertApi';

const ServiceForm: React.FC<{
    type: 'ADD' | 'EDIT' | 'DETAIL';
    initValue?: any;
    onSubmit?: (values: any) => void;
    loading?: boolean;
}> = ({ type, initValue, onSubmit, loading = false }) => {
    const formRef = useRef<ProFormInstance>(null);
    const serviceProfileUploadRef = useRef<any>();
    const serviceIntroductionUploadRef = useRef<any>();
    const casePresentationUploadRef = useRef<any>();
    const richTextRef = useRef<{ getRichTextLength: () => number }>();
    const { codeInfo, initCode } = useModel('codeState');
    const { run: queryServiceTypeConfig, data: serviceTypeConfigData } = useRequest(
        getServiceTypeListApi,
        {
            manual: true,
        },
    );

    useEffect(() => {
        if (isEmpty(codeInfo?.serviceModule)) {
            initCode('serviceModule');
        }
        if (isEmpty(codeInfo?.serviceType)) {
            initCode('serviceType');
        }
        if (isEmpty(codeInfo?.servicePriceUnit)) {
            initCode('servicePriceUnit');
        }
        queryServiceTypeConfig();
    }, []);

    useEffect(() => {
        if (initValue) {
            if (initValue.serviceProfilePic) {
                serviceProfileUploadRef.current?.init([
                    {
                        uid: initValue.serviceProfilePic,
                        name: initValue.serviceProfilePic,
                        status: 'done',
                        url: initValue.serviceProfilePicUrl,
                    },
                ]);
            }
            if (initValue.serviceIntroductionPic) {
                serviceIntroductionUploadRef.current?.init([
                    {
                        uid: initValue.serviceIntroductionPic,
                        name: initValue.serviceIntroductionPic,
                        status: 'done',
                        url: initValue.serviceIntroductionPicUrl,
                    },
                ]);
            }
            setTimeout(() => {
                if (initValue.casePresentationPic) {
                    casePresentationUploadRef.current?.init([
                        {
                            uid: initValue.casePresentationPic,
                            name: initValue.casePresentationPic,
                            status: 'done',
                            url: initValue.casePresentationPicUrl,
                        },
                    ]);
                }
            }, 30);
            formRef.current?.setFieldsValue({
                ...initValue,
                serviceLabel: initValue?.serviceLabel?.split(','),
            });
        }
    }, [initValue]);

    const allDisabled = type === 'DETAIL';
    const isEditing = type === 'EDIT';

    const submitForm = async () => {
        await formRef.current?.validateFields();
        const values = formRef.current?.getFieldsFormatValue?.(true);
        console.debug('submitForm values', values);
        const parmas = {
            ...values,
            fixedPriceService: values?.discountFlag ? values?.fixedPriceService : undefined,
            serviceLabel: Array.isArray(values?.serviceLabel)
                ? values?.serviceLabel?.join(',')
                : values?.serviceLabel,
        };
        onSubmit?.(parmas);
    };

    return (
        <ProForm
            formRef={formRef}
            layout="horizontal"
            submitter={false}
            wrapperCol={{ span: 12 }}
            labelCol={{ flex: '0 0 140px' }}
            labelAlign="right"
            scrollToFirstError
            preserve={type !== 'ADD'}
        >
            <ProFormGroup title="基本信息" />
            <ProFormSelect
                label="服务类目"
                name="serviceModule"
                required
                rules={[{ required: true, message: '请选择服务类目' }]}
                options={codeInfo?.serviceModule?.map((item: any) => {
                    return {
                        label: item.codeName,
                        value: item.codeValue,
                    };
                })}
                onChange={() => {
                    formRef.current?.resetFields(['serviceType']);
                }}
                disabled={allDisabled || isEditing}
            />
            <ProFormDependency name={['serviceModule']}>
                {({ serviceModule }) => {
                    const options: any[] = [];
                    if (serviceModule) {
                        serviceTypeConfigData?.data?.forEach((item: any) => {
                            if (item.serviceModule === serviceModule) {
                                options.push({
                                    label: codeInfo?.serviceType?.find(
                                        (v: any) => v.codeValue === item.serviceType,
                                    )?.codeName,
                                    value: item.serviceType,
                                });
                            }
                        });
                    }
                    return (
                        <ProFormSelect
                            label="服务类型"
                            name="serviceType"
                            required
                            rules={[{ required: true, message: '请选择服务类型' }]}
                            disabled={allDisabled || isEditing}
                            options={options}
                            onChange={(serviceType: any) => {
                                const config = serviceTypeConfigData?.data?.find(
                                    (v) => v.serviceType === serviceType,
                                );
                                formRef.current?.setFieldsValue({
                                    configType: config?.configType || ConfigTypeEnum.LEAD_RETURN,
                                    servicePriceUnit: config?.servicePriceUnit ?? undefined,
                                });
                            }}
                        />
                    );
                }}
            </ProFormDependency>
            <ProFormItem noStyle name="servicePriceUnit">
                <Input type="hidden" />
            </ProFormItem>
            <ProFormSelect
                label="配置类型"
                name="configType"
                required
                rules={[{ required: true }]}
                options={[
                    {
                        label: '自主开通',
                        value: ConfigTypeEnum.SELF_OPEN,
                    },
                    {
                        label: '留资回访',
                        value: ConfigTypeEnum.LEAD_RETURN,
                    },
                ]}
                disabled
            />
            <ProFormText
                label="服务名称"
                name="serviceName"
                required
                rules={[{ required: true, message: '请输入服务名称', whitespace: true }]}
                transform={(v) => v?.trim()}
                fieldProps={{
                    maxLength: 20,
                    showCount: true,
                    placeholder: '请输入服务名称',
                }}
                disabled={allDisabled}
            />
            <ProFormText
                label="服务副标题"
                name="serviceSubtitle"
                required
                rules={[{ required: true, message: '请输入服务副标题', whitespace: true }]}
                transform={(v) => v?.trim()}
                fieldProps={{
                    maxLength: 40,
                    showCount: true,
                    placeholder: '请输入服务副标题',
                }}
                disabled={allDisabled}
            />
            <ProFormItem
                label="服务头图"
                name="serviceProfilePic"
                required
                rules={[{ required: true, message: '请上传服务头图' }]}
            >
                <UpLoadImgItem
                    uploadData={{
                        contentType: '02',
                        contRemrk: 'servicePic',
                        relaTable: 'cr_service',
                    }}
                    sizeInfo={{ size: 200, width: 184, height: 184 }}
                    ref={serviceProfileUploadRef}
                    disabled={allDisabled}
                    maxCount={1}
                    returnRelativePath
                />
            </ProFormItem>
            <ProFormDependency name={['configType', 'servicePriceUnit']}>
                {({ configType, servicePriceUnit }) => {
                    return (
                        configType === ConfigTypeEnum.SELF_OPEN && (
                            <>
                                <QuillRichInput
                                    label="服务说明"
                                    name="serviceDesc"
                                    required
                                    ref={richTextRef}
                                    rules={[
                                        { required: true, message: '请输入服务说明' },
                                        () => ({
                                            validator(rule: any, value: string) {
                                                if (value == '<p><br></p>') {
                                                    return Promise.reject('请输入正文内容');
                                                }
                                                const markLength =
                                                    richTextRef?.current?.getRichTextLength() || 0;

                                                if (value && markLength - 1 > 50000) {
                                                    return Promise.reject('限50000个字');
                                                }
                                                return Promise.resolve();
                                            },
                                        }),
                                    ]}
                                    initialValue={''}
                                    placeholder="请填写服务说明"
                                    disabled={allDisabled}
                                />
                                <ProFormItem
                                    label="服务标签"
                                    name="serviceLabel"
                                    transform={(v) => (Array.isArray(v) ? v?.join(',') : v)}
                                >
                                    <ServiceTag disabled={allDisabled} />
                                </ProFormItem>
                                <ProFormItem
                                    label="服务单价"
                                    name="servicePrice"
                                    required
                                    rules={[{ required: true, message: '请输入服务单价' }]}
                                >
                                    <InputNumber
                                        addonAfter={
                                            codeInfo?.servicePriceUnit?.find(
                                                (v: API.CodeInfo) =>
                                                    v.codeValue === servicePriceUnit,
                                            )?.codeName
                                        }
                                        precision={2}
                                        min={0}
                                        max={99999}
                                        disabled={allDisabled}
                                        style={{ width: '168px' }}
                                    />
                                </ProFormItem>
                                <ProFormRadio.Group
                                    label="是否享受优惠"
                                    name="discountFlag"
                                    initialValue={false}
                                    options={[
                                        {
                                            label: '是',
                                            value: true,
                                        },
                                        {
                                            label: '否',
                                            value: false,
                                        },
                                    ]}
                                    disabled={allDisabled}
                                />
                                <ProFormDependency name={['discountFlag']}>
                                    {({ discountFlag }) => {
                                        return (
                                            discountFlag && (
                                                <ProFormItem
                                                    label="服务一口价"
                                                    name="fixedPriceService"
                                                    required
                                                    rules={[
                                                        {
                                                            required: true,
                                                            message: '请输入服务一口价',
                                                        },
                                                        {
                                                            validator(rule, value) {
                                                                if (value) {
                                                                    //一口价不能大于等于服务单价
                                                                    const servicePrice =
                                                                        formRef.current?.getFieldValue(
                                                                            'servicePrice',
                                                                        );
                                                                    if (
                                                                        value >= (servicePrice || 0)
                                                                    ) {
                                                                        return Promise.reject(
                                                                            '服务一口价不能大于等于服务单价',
                                                                        );
                                                                    }
                                                                }
                                                                return Promise.resolve();
                                                            },
                                                        },
                                                    ]}
                                                >
                                                    <InputNumber
                                                        addonAfter={
                                                            codeInfo?.servicePriceUnit?.find(
                                                                (v: API.CodeInfo) =>
                                                                    v.codeValue ===
                                                                    servicePriceUnit,
                                                            )?.codeName
                                                        }
                                                        precision={2}
                                                        min={0.01}
                                                        max={99999}
                                                        disabled={allDisabled}
                                                    />
                                                </ProFormItem>
                                            )
                                        );
                                    }}
                                </ProFormDependency>
                                {/* { <ProFormItem
                                    label="库存数量"
                                    name="inventoryNum"
                                    required
                                    rules={[
                                        { required: true, message: '请输入库存数量' },
                                        {
                                            validator(rule, value) {
                                                //仅支持输入-1和大于0的正整数
                                                if (
                                                    value === -1 ||
                                                    (value > 0 && Number.isInteger(value))
                                                ) {
                                                    return Promise.resolve();
                                                } else {
                                                    return Promise.reject(
                                                        '请输入-1或大于0的正整数',
                                                    );
                                                }
                                            },
                                        },
                                    ]}
                                    help="-1表示不限制库存"
                                >
                                    <InputNumber
                                        precision={0}
                                        min={-1}
                                        max={999999999}
                                        disabled={allDisabled || isEditing}
                                        addonAfter="份"
                                    />
                                </ProFormItem>
                                {isEditing && initValue?.inventoryNum !== -1 && (
                                    <ProFormItem label="追加库存" name="inventoryAppend">
                                        <InputNumber
                                            precision={0}
                                            min={1}
                                            max={999999999}
                                            placeholder="请输入追加库存"
                                            addonAfter="份"
                                        />
                                    </ProFormItem>
                                )}} */}
                            </>
                        )
                    );
                }}
            </ProFormDependency>
            <ProFormGroup title="服务展示" />
            <ProFormDependency name={['configType']}>
                {({ configType }) => {
                    return (
                        <>
                            <ProForm.Item
                                label="服务介绍"
                                required={configType === ConfigTypeEnum.LEAD_RETURN}
                                rules={
                                    configType === ConfigTypeEnum.LEAD_RETURN
                                        ? [{ required: true, message: '请上传服务介绍' }]
                                        : undefined
                                }
                                name="serviceIntroductionPic"
                            >
                                <UpLoadImgItem
                                    uploadData={{
                                        contentType: '02',
                                        contRemrk: 'servicePic',
                                        relaTable: 'cr_service',
                                    }}
                                    sizeInfo={{ size: 2048, width: 1500 }}
                                    ref={serviceIntroductionUploadRef}
                                    placeholder="上传尺寸宽1500*高度自适应，格式支持png、jpg、jpeg、gif，大小不得超过2MB"
                                    disabled={allDisabled}
                                    maxCount={1}
                                    returnRelativePath
                                />
                            </ProForm.Item>
                            {configType === ConfigTypeEnum.SELF_OPEN && (
                                <ProForm.Item label="案例演示" name="casePresentationPic">
                                    <UpLoadImgItem
                                        uploadData={{
                                            contentType: '02',
                                            contRemrk: 'servicePic',
                                            relaTable: 'cr_service',
                                        }}
                                        sizeInfo={{ size: 2048, width: 1500 }}
                                        ref={casePresentationUploadRef}
                                        placeholder="上传尺寸宽1500*高度自适应，格式支持png、jpg、jpeg、gif，大小不得超过2MB"
                                        disabled={allDisabled}
                                        maxCount={1}
                                        returnRelativePath
                                    />
                                </ProForm.Item>
                            )}
                            {configType === ConfigTypeEnum.LEAD_RETURN && (
                                <ProFormText
                                    label="我司联系方式"
                                    name="myCompanyTelephone"
                                    fieldProps={{
                                        maxLength: 11,
                                    }}
                                    placeholder="请填写手机号"
                                    required
                                    rules={[
                                        { required: true, message: '请输入手机号' },
                                        {
                                            pattern: /^[1-9]\d{10}$/,
                                            message: '请输入正确的手机号',
                                        },
                                    ]}
                                    transform={(v) => v?.trim()}
                                    disabled={allDisabled}
                                />
                            )}
                        </>
                    );
                }}
            </ProFormDependency>
            <ProFormGroup title="系统配置" />
            <ProFormDependency name={['configType']}>
                {({ configType }) => {
                    return (
                        <>
                            {configType === ConfigTypeEnum.SELF_OPEN && (
                                <>
                                    <ProFormSelect
                                        label="财务结算方案"
                                        name="financeConfigId"
                                        required
                                        rules={[
                                            {
                                                required: true,
                                                message: '请选择方案或前往服务配置填写财务信息！',
                                            },
                                        ]}
                                        request={() => {
                                            return getServiceFinanceConfigListApi().then(
                                                (res: any) => {
                                                    return (
                                                        res?.data?.map(
                                                            (item: API.ServiceFinanceConfigVo) => {
                                                                return {
                                                                    label: item.protocolName,
                                                                    value: item.id,
                                                                    raw: item,
                                                                };
                                                            },
                                                        ) || []
                                                    );
                                                },
                                            );
                                        }}
                                        disabled={allDisabled}
                                    />
                                    <ProFormItem
                                        label="新电途分成比例"
                                        name="xdtShareRatio"
                                        initialValue={100}
                                        required
                                        rules={[
                                            { required: true, message: '请输入新电途分成比例' },
                                        ]}
                                    >
                                        <InputNumber
                                            disabled
                                            placeholder="请输入新电途分成比例"
                                            precision={2}
                                            min={0}
                                            max={100}
                                            addonAfter="%"
                                        />
                                    </ProFormItem>
                                    {/* <ProFormText
                                        label="服务商ID"
                                        name="serviceProviderId"
                                        placeholder="请输入服务商ID"
                                        transform={(v) => v?.trim()}
                                        disabled={allDisabled}
                                    />
                                    <ProFormItem
                                        label="服务商分成比例"
                                        name="operShareRatio"
                                        rules={[
                                            {
                                                validator(rule, value) {
                                                    const xdtShareRatio =
                                                        formRef.current?.getFieldValue(
                                                            'xdtShareRatio',
                                                        );
                                                    if (
                                                        Number(value ?? 0) +
                                                            Number(xdtShareRatio ?? 0) !==
                                                        100
                                                    ) {
                                                        return Promise.reject(
                                                            '与新电途分成比例之和需等于100%，请确认后重新填写',
                                                        );
                                                    }
                                                    return Promise.resolve();
                                                },
                                            },
                                        ]}
                                    >
                                        <InputNumber
                                            disabled={allDisabled}
                                            placeholder="请输入服务商分成比例"
                                            precision={2}
                                            min={0}
                                            max={100}
                                            addonAfter="%"
                                        />
                                    </ProFormItem> */}
                                    <ProFormText
                                        label="服务市场协议编号"
                                        name="serviceMktAgreementNo"
                                        placeholder="请输入服务市场协议编号"
                                        fieldProps={{
                                            maxLength: 10,
                                        }}
                                        transform={(v) => v?.trim()}
                                        required
                                        rules={[
                                            { required: true, message: '请输入服务市场协议编号' },
                                        ]}
                                        disabled={allDisabled}
                                    />
                                    <ProFormText
                                        label="服务协议编号"
                                        name="serviceAgreementNo"
                                        placeholder="请输入服务协议编号"
                                        fieldProps={{
                                            maxLength: 10,
                                        }}
                                        transform={(v) => v?.trim()}
                                        required
                                        rules={[{ required: true, message: '请输入服务协议编号' }]}
                                        disabled={allDisabled}
                                    />
                                </>
                            )}
                            <ProFormDigit
                                label="展示优先级"
                                name="displayPriority"
                                required
                                rules={[{ required: true, message: '请输入展示优先级' }]}
                                fieldProps={{
                                    precision: 0,
                                    min: 0,
                                    max: 99999,
                                }}
                                disabled={allDisabled}
                            />
                        </>
                    );
                }}
            </ProFormDependency>
            {type !== 'DETAIL' && (
                <Row>
                    <Col offset={8}>
                        <Space size="large">
                            <Button
                                onClick={() => {
                                    formRef.current?.resetFields();
                                    history.goBack();
                                }}
                            >
                                取消
                            </Button>
                            <Button type="primary" onClick={submitForm} loading={loading}>
                                {type === 'ADD' ? '创建' : '保存'}
                            </Button>
                        </Space>
                    </Col>
                </Row>
            )}
        </ProForm>
    );
};

export default ServiceForm;
